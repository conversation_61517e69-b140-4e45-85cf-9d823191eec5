<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الحاسبات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .back-btn {
            background: #e74c3c;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .back-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .computers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        .computer-card {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .computer-card:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.2);
        }

        .computer-card.active {
            border-color: #2ecc71;
            background: #f8fff9;
        }

        .computer-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .computer-icon {
            font-size: 2.5em;
            margin-left: 15px;
            color: #3498db;
        }

        .computer-info h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.4em;
        }

        .computer-info p {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .computer-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .computer-details {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #3498db;
        }

        .computer-details.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .permissions-section {
            margin-top: 20px;
        }

        .permissions-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ecf0f1;
        }

        .permission-item input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }

        .permission-item label {
            font-size: 0.9em;
            color: #2c3e50;
            cursor: pointer;
        }

        .update-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 15px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .update-btn:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        /* منع التمرير غير المرغوب فيه */
        .computer-details * {
            scroll-behavior: auto;
        }

        .permission-item {
            position: relative;
        }

        .status-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-active {
            background: #d5f4e6;
            color: #27ae60;
        }

        .status-inactive {
            background: #fadbd8;
            color: #e74c3c;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }

        .no-computers {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .credentials-section {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 10px;
            border: 1px solid #ffeaa7;
        }

        .credentials-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .credential-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }

        .credential-label {
            font-weight: bold;
            color: #2c3e50;
            width: 80px;
            margin-left: 10px;
        }

        .credential-value {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }

        .credential-value:focus {
            border-color: #3498db;
            outline: none;
        }

        .password-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .change-password-btn {
            background: #f39c12;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .change-password-btn:hover {
            background: #e67e22;
        }

        .save-password-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .save-password-btn:hover {
            background: #229954;
        }

        .cancel-password-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .cancel-password-btn:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/admin/dashboard" class="back-btn">🔙 العودة لوحة التحكم</a>
            <h1>💻 قائمة الحاسبات المسجلة</h1>
        </div>

        <div class="computers-grid" id="computersGrid">
            <div class="loading">
                <div>⏳ جاري تحميل البيانات...</div>
            </div>
        </div>
    </div>

    <script>
        let computers = {};
        let activeCard = null;

        // تحميل بيانات الحاسبات
        async function loadComputers() {
            try {
                const response = await fetch('/admin/api/computers');
                const data = await response.json();
                
                if (data.success) {
                    computers = data.computers;
                    renderComputers();
                } else {
                    showError('فشل في تحميل بيانات الحاسبات');
                }
            } catch (error) {
                console.error('Error loading computers:', error);
                showError('خطأ في الاتصال بالخادم');
            }
        }

        // عرض الحاسبات
        function renderComputers() {
            const grid = document.getElementById('computersGrid');
            
            if (Object.keys(computers).length === 0) {
                grid.innerHTML = '<div class="no-computers">لا توجد حاسبات مسجلة</div>';
                return;
            }

            let html = '';
            Object.entries(computers).forEach(([name, data]) => {
                const hasContract = data.has_saved_contract;
                const statusClass = hasContract ? 'status-active' : 'status-inactive';
                const statusText = hasContract ? 'نشط' : 'غير نشط';
                
                html += `
                    <div class="computer-card" onclick="toggleDetails('${name}', event)">
                        <div class="status-badge ${statusClass}">${statusText}</div>
                        
                        <div class="computer-header">
                            <div class="computer-icon">💻</div>
                            <div class="computer-info">
                                <h3>${name}</h3>
                                <p>ID: ${data.id}</p>
                            </div>
                        </div>

                        <div class="computer-stats">
                            <div class="stat-item">
                                <span class="stat-value">${data.contracts_count || 0}</span>
                                <div class="stat-label">عدد العقود</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${hasContract ? '✓' : '✗'}</span>
                                <div class="stat-label">عقد محفوظ</div>
                            </div>
                        </div>

                        <div class="computer-details" id="details_${name}">
                            <div><strong>تاريخ التسجيل:</strong> ${data.created_at}</div>

                            <div class="credentials-section">
                                <div class="credentials-title">🔑 بيانات الدخول</div>
                                <div class="credential-item">
                                    <span class="credential-label">اليوزر:</span>
                                    <input type="text" class="credential-value" value="${name}" readonly>
                                </div>
                                <div class="credential-item">
                                    <span class="credential-label">الباسوورد:</span>
                                    <input type="password" class="credential-value" id="password_${name}" value="${data.password}" readonly>
                                </div>
                                <div class="password-actions">
                                    <button class="change-password-btn" onclick="enablePasswordEdit('${name}', event)">
                                        ✏️ تغيير كلمة المرور
                                    </button>
                                    <button class="save-password-btn" id="save_${name}" style="display: none;" onclick="savePassword('${name}', event)">
                                        💾 حفظ
                                    </button>
                                    <button class="cancel-password-btn" id="cancel_${name}" style="display: none;" onclick="cancelPasswordEdit('${name}', event)">
                                        ❌ إلغاء
                                    </button>
                                </div>
                            </div>

                            <div class="permissions-section">
                                <div class="permissions-title">🔐 الصلاحيات</div>
                                <div class="permissions-grid">
                                    <div class="permission-item">
                                        <input type="checkbox" id="create_${name}" ${data.permissions?.create_contracts ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="create_${name}" onclick="event.stopPropagation()">إنشاء عقود جديدة</label>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" id="edit_${name}" ${data.permissions?.edit_saved_contracts ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="edit_${name}" onclick="event.stopPropagation()">تعديل العقود المحفوظة</label>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" id="view_${name}" ${data.permissions?.view_serial_number ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="view_${name}" onclick="event.stopPropagation()">عرض الرقم التسلسلي</label>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" id="edit_finalized_${name}" ${data.permissions?.edit_finalized_contracts ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="edit_finalized_${name}" onclick="event.stopPropagation()">تعديل العقود المبرمة</label>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" id="download_finalized_${name}" ${data.permissions?.download_finalized_contracts ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="download_finalized_${name}" onclick="event.stopPropagation()">تحميل العقود المبرمة</label>
                                    </div>
                                </div>
                                
                                <button class="update-btn" onclick="updatePermissions('${name}', event)">
                                    💾 تحديث الصلاحيات
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        }

        // تبديل عرض التفاصيل
        function toggleDetails(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const card = event ? event.currentTarget : document.querySelector(`[onclick*="${computerName}"]`);
            const details = document.getElementById(`details_${computerName}`);

            // إغلاق البطاقة النشطة السابقة
            if (activeCard && activeCard !== card) {
                activeCard.classList.remove('active');
                const prevDetails = activeCard.querySelector('.computer-details');
                if (prevDetails) {
                    prevDetails.classList.remove('show');
                }
            }

            // تبديل البطاقة الحالية
            card.classList.toggle('active');
            details.classList.toggle('show');

            activeCard = card.classList.contains('active') ? card : null;
        }

        // تحديث صلاحيات حاسوب معين
        async function updatePermissions(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation(); // منع تفعيل toggleDetails
            }

            const permissions = {
                create_contracts: document.getElementById(`create_${computerName}`).checked,
                edit_saved_contracts: document.getElementById(`edit_${computerName}`).checked,
                view_serial_number: document.getElementById(`view_${computerName}`).checked,
                edit_finalized_contracts: document.getElementById(`edit_finalized_${computerName}`).checked,
                download_finalized_contracts: document.getElementById(`download_finalized_${computerName}`).checked
            };

            try {
                const response = await fetch('/admin/api/permissions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        computer_name: computerName,
                        permissions: permissions
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    alert('تم تحديث الصلاحيات بنجاح');
                    // تحديث البيانات المحلية
                    computers[computerName].permissions = permissions;
                } else {
                    alert('خطأ في تحديث الصلاحيات: ' + data.message);
                }
            } catch (error) {
                console.error('Error updating permissions:', error);
                alert('خطأ في تحديث الصلاحيات');
            }
        }

        // تفعيل تعديل كلمة المرور
        function enablePasswordEdit(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const passwordInput = document.getElementById(`password_${computerName}`);
            const saveBtn = document.getElementById(`save_${computerName}`);
            const cancelBtn = document.getElementById(`cancel_${computerName}`);
            const changeBtn = event.target;

            // تفعيل حقل كلمة المرور
            passwordInput.readOnly = false;
            passwordInput.type = 'text';
            passwordInput.focus();
            passwordInput.select();

            // إظهار أزرار الحفظ والإلغاء
            saveBtn.style.display = 'inline-block';
            cancelBtn.style.display = 'inline-block';
            changeBtn.style.display = 'none';

            // حفظ القيمة الأصلية
            passwordInput.dataset.originalValue = passwordInput.value;
        }

        // حفظ كلمة المرور الجديدة
        async function savePassword(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const passwordInput = document.getElementById(`password_${computerName}`);
            const newPassword = passwordInput.value.trim();

            if (!newPassword) {
                alert('يرجى إدخال كلمة مرور صالحة');
                return;
            }

            try {
                const response = await fetch('/admin/api/change_computer_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        computer_name: computerName,
                        new_password: newPassword
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('تم تغيير كلمة المرور بنجاح');
                    // تحديث البيانات المحلية
                    computers[computerName].password = newPassword;
                    cancelPasswordEdit(computerName);
                } else {
                    alert('خطأ في تغيير كلمة المرور: ' + data.message);
                }
            } catch (error) {
                console.error('Error changing password:', error);
                alert('خطأ في تغيير كلمة المرور');
            }
        }

        // إلغاء تعديل كلمة المرور
        function cancelPasswordEdit(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const passwordInput = document.getElementById(`password_${computerName}`);
            const saveBtn = document.getElementById(`save_${computerName}`);
            const cancelBtn = document.getElementById(`cancel_${computerName}`);
            const changeBtn = passwordInput.closest('.credentials-section').querySelector('.change-password-btn');

            // استعادة القيمة الأصلية
            passwordInput.value = passwordInput.dataset.originalValue || computers[computerName].password;

            // تعطيل حقل كلمة المرور
            passwordInput.readOnly = true;
            passwordInput.type = 'password';

            // إخفاء أزرار الحفظ والإلغاء
            saveBtn.style.display = 'none';
            cancelBtn.style.display = 'none';
            changeBtn.style.display = 'inline-block';
        }

        // عرض رسالة خطأ
        function showError(message) {
            const grid = document.getElementById('computersGrid');
            grid.innerHTML = `<div class="no-computers">❌ ${message}</div>`;
        }

        // منع التمرير غير المرغوب فيه
        function preventScroll(event) {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadComputers();

            // منع التمرير عند النقر على العناصر التفاعلية
            document.addEventListener('click', function(e) {
                if (e.target.matches('input[type="checkbox"]') ||
                    e.target.matches('label') ||
                    e.target.matches('.update-btn')) {
                    e.stopPropagation();
                }
            });
        });
    </script>
</body>
</html>
